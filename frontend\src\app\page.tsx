// frontend/src/app/page.tsx
'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Store, MessageSquare, CreditCard, Users } from 'lucide-react';

export default function Home() {
  return (
    <div className="bg-slate-900">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white min-h-[80vh] w-full animate-in fade-in-0 slide-in-from-bottom-4 duration-700 px-2 sm:px-4 overflow-hidden">
        {/* Background image */}
        <Image
          src="/images/hero-bg.jpg"
          alt="Marketplace background"
          fill
          style={{ objectFit: 'cover', objectPosition: 'center' }}
          priority
        />
        {/* Overlay */}
        <div className="absolute inset-0 bg-slate-900/70 z-10" />
        <div className="relative z-20 max-w-7xl mx-auto px-4 py-24 flex flex-col items-center justify-center min-h-[80vh] text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white drop-shadow-lg">
            Grow Faster. Connect Smarter.<br />Empower Your Business
          </h1>
          <p className="text-lg md:text-xl text-slate-200 mb-8 max-w-2xl mx-auto drop-shadow">
            Market O'Clock is the all-in-one B2B2C marketplace where suppliers, retailers, and businesses connect, collaborate, and thrive—powered by social engagement and seamless commerce.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/register">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 shadow-lg text-lg font-semibold">
                Get Started
              </Button>
            </Link>
            <Link href="/about">
              <Button size="lg" variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-800 px-8 py-3 text-lg font-semibold">
                Learn More
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="secondary" className="bg-green-600 hover:bg-green-700 text-white px-8 py-3 text-lg font-semibold">
                Book a Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Customer Logos Section */}
      <section className="py-8 bg-slate-900 animate-in fade-in-0 slide-in-from-bottom-4 duration-700 delay-100 px-2 sm:px-4">
        <div className="max-w-5xl mx-auto px-4">
          <div className="flex flex-col items-center mb-4">
            <span className="uppercase text-xs text-slate-400 tracking-widest mb-2">Trusted by businesses</span>
            {/* Logos removed for cleaner look */}
          </div>
        </div>
      </section>

      {/* Key Features Section */}
      <section className="py-20 bg-slate-900 animate-in fade-in-0 slide-in-from-bottom-4 duration-700 delay-200 px-2 sm:px-4">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Key Features</h2>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto">
              Discover what makes Market O'Clock the ultimate B2B2C marketplace platform.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8">
            {/* Marketplace Feature */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 hover:border-slate-600 transition-all duration-300 hover:transform hover:scale-105">
              <div className="mb-6">
                <Store className="h-10 w-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Marketplace</h3>
              <p className="text-slate-400 leading-relaxed">
                Discover new partners and opportunities in our efficient, easy-to-use marketplace platform.
              </p>
            </div>

            {/* Microblogs & Blogs Feature */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 hover:border-slate-600 transition-all duration-300 hover:transform hover:scale-105">
              <div className="mb-6">
                <MessageSquare className="h-10 w-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Microblogs & Blogs</h3>
              <p className="text-slate-400 leading-relaxed">
                Boost your brand and products with engaging content and interactive social features.
              </p>
            </div>

            {/* Multi-Payment Support Feature */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 hover:border-slate-600 transition-all duration-300 hover:transform hover:scale-105">
              <div className="mb-6">
                <CreditCard className="h-10 w-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Multi-Payment Support</h3>
              <p className="text-slate-400 leading-relaxed">
                Enjoy secure, flexible payment options for every transaction—built for business.
              </p>
            </div>

            {/* Community Feature */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 hover:border-slate-600 transition-all duration-300 hover:transform hover:scale-105">
              <div className="mb-6">
                <Users className="h-10 w-10 text-blue-400" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Community</h3>
              <p className="text-slate-400 leading-relaxed">
                Join a vibrant network of businesses and retailers for support, learning, and growth.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-slate-900 animate-in fade-in-0 slide-in-from-bottom-4 duration-700 delay-300 px-2 sm:px-4">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">What Our Customers Say</h2>
            <p className="text-lg text-slate-400 max-w-2xl mx-auto">
              Hear from businesses and retailers who use Market O'Clock to grow and connect.
            </p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 shadow-lg flex flex-col items-center">
              <div className="w-16 h-16 rounded-full bg-slate-700 mb-4 flex items-center justify-center text-2xl text-blue-400">A</div>
              <p className="text-slate-300 italic mb-4">“Market O'Clock made it easy to find new suppliers and grow our business network.”</p>
              <span className="font-semibold text-white">Alice Smith</span>
              <span className="text-slate-400 text-sm">Retailer, ShopEase</span>
            </div>
            {/* Testimonial 2 */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 shadow-lg flex flex-col items-center">
              <div className="w-16 h-16 rounded-full bg-slate-700 mb-4 flex items-center justify-center text-2xl text-blue-400">B</div>
              <p className="text-slate-300 italic mb-4">“The social features helped us engage with our customers and boost product visibility.”</p>
              <span className="font-semibold text-white">Brian Lee</span>
              <span className="text-slate-400 text-sm">Supplier, FreshMart</span>
            </div>
            {/* Testimonial 3 */}
            <div className="bg-slate-800 rounded-lg p-8 border border-slate-700 shadow-lg flex flex-col items-center">
              <div className="w-16 h-16 rounded-full bg-slate-700 mb-4 flex items-center justify-center text-2xl text-blue-400">C</div>
              <p className="text-slate-300 italic mb-4">“Secure payments and a seamless experience—highly recommended!”</p>
              <span className="font-semibold text-white">Carla Gomez</span>
              <span className="text-slate-400 text-sm">Business Owner, TrendyGoods</span>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-12 bg-slate-900 animate-in fade-in-0 slide-in-from-bottom-4 duration-700 delay-350 px-2 sm:px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">Featured In</h2>
          {/* Press logos removed for cleaner look */}
          <div className="flex flex-col items-center">
            <span className="text-lg text-slate-300 mb-2">Rated 5 Stars by Our Users</span>
            <div className="flex gap-1 justify-center mb-2">
              <svg className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.175c.969 0 1.371 1.24.588 1.81l-3.38 2.455a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.38-2.454a1 1 0 00-1.175 0l-3.38 2.454c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.05 9.394c-.783-.57-.38-1.81.588-1.81h4.175a1 1 0 00.95-.69l1.286-3.967z"/></svg>
              <svg className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.175c.969 0 1.371 1.24.588 1.81l-3.38 2.455a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.38-2.454a1 1 0 00-1.175 0l-3.38 2.454c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.05 9.394c-.783-.57-.38-1.81.588-1.81h4.175a1 1 0 00.95-.69l1.286-3.967z"/></svg>
              <svg className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.175c.969 0 1.371 1.24.588 1.81l-3.38 2.455a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.38-2.454a1 1 0 00-1.175 0l-3.38 2.454c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.05 9.394c-.783-.57-.38-1.81.588-1.81h4.175a1 1 0 00.95-.69l1.286-3.967z"/></svg>
              <svg className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.175c.969 0 1.371 1.24.588 1.81l-3.38 2.455a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.38-2.454a1 1 0 00-1.175 0l-3.38 2.454c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.05 9.394c-.783-.57-.38-1.81.588-1.81h4.175a1 1 0 00.95-.69l1.286-3.967z"/></svg>
              <svg className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20"><path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.286 3.967a1 1 0 00.95.69h4.175c.969 0 1.371 1.24.588 1.81l-3.38 2.455a1 1 0 00-.364 1.118l1.287 3.966c.3.922-.755 1.688-1.54 1.118l-3.38-2.454a1 1 0 00-1.175 0l-3.38 2.454c-.784.57-1.838-.196-1.54-1.118l1.287-3.966a1 1 0 00-.364-1.118L2.05 9.394c-.783-.57-.38-1.81.588-1.81h4.175a1 1 0 00.95-.69l1.286-3.967z"/></svg>
            </div>
            <span className="text-slate-400 text-sm">Based on 500+ reviews</span>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-slate-800 animate-in fade-in-0 slide-in-from-bottom-4 duration-700 delay-400 px-2 sm:px-4">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to grow your business?
          </h2>
          <p className="text-lg text-slate-400 mb-10 max-w-2xl mx-auto">
            Join Market O'Clock today and connect with suppliers and retailers from various industries.
          </p>
          <Link href="/register">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg font-semibold shadow-lg mr-4">
              Sign Up Now
            </Button>
          </Link>
          <Link href="/contact">
            <Button size="lg" variant="secondary" className="bg-green-600 hover:bg-green-700 text-white px-8 py-4 text-lg font-semibold">
            Book a Demo
          </Button>
        </Link>
      </div>
    </section>
  </div>
);
}