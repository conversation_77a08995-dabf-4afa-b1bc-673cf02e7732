// src/components/footer.tsx
'use client';

import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-slate-900 text-slate-300 border-t border-slate-700 py-8 md:py-12 px-4 md:px-6" aria-label="Site footer">
      <div className="max-w-7xl mx-auto">
        {/* Mobile Layout */}
        <div className="block md:hidden space-y-8">
          {/* Company Info - Mobile */}
          <section aria-labelledby="footer-company-title-mobile">
            <h3 id="footer-company-title-mobile" className="text-white font-bold mb-4 text-lg">Market O&apos;Clock</h3>
            <p className="text-slate-400 leading-relaxed text-sm">
              Market O&apos;Clock connects suppliers through innovative marketplace solutions with social engagement.
            </p>
          </section>

          {/* Navigation Links - Mobile */}
          <div className="grid grid-cols-2 gap-6">
            <nav aria-label="Marketplace Mobile" className="text-sm">
              <h4 className="text-white font-semibold mb-3">Marketplace</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                    Suppliers
                  </Link>
                </li>
                <li>
                  <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                    Popular Items
                  </Link>
                </li>
                <li>
                  <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                    New Arrivals
                  </Link>
                </li>
              </ul>
            </nav>

            <nav aria-label="Company Mobile" className="text-sm">
              <h4 className="text-white font-semibold mb-3">Company</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/about" className="text-slate-400 hover:text-blue-400 transition-colors">
                    About
                  </Link>
                </li>
                <li>
                  <Link href="/blog" className="text-slate-400 hover:text-blue-400 transition-colors">
                    Blogs
                  </Link>
                </li>
                <li>
                  <Link href="/careers" className="text-slate-400 hover:text-blue-400 transition-colors">
                    Careers
                  </Link>
                </li>
              </ul>
            </nav>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <nav aria-label="Legal Mobile" className="text-sm">
              <h4 className="text-white font-semibold mb-3">Legal</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/terms" className="text-slate-400 hover:text-blue-400 transition-colors">
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link href="/privacy" className="text-slate-400 hover:text-blue-400 transition-colors">
                    Privacy Policy
                  </Link>
                </li>
              </ul>
            </nav>

            <div className="text-sm">
              <h4 className="text-white font-semibold mb-3">Contact</h4>
              <ul className="space-y-2">
                <li>
                  <a href="mailto:<EMAIL>" className="text-slate-400 hover:text-blue-400 transition-colors">
                    <EMAIL>
                  </a>
                </li>
                <li className="text-slate-500">
                  Nairobi, Kenya
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info - Desktop */}
          <section aria-labelledby="footer-company-title">
            <h3 id="footer-company-title" className="text-white font-bold mb-4 text-lg">Market O&apos;Clock</h3>
            <p className="text-slate-400 leading-relaxed text-sm max-w-md">
              Market O&apos;Clock connects suppliers through innovative marketplace solutions with social engagement.
            </p>
          </section>

          {/* Marketplace Links - Desktop */}
          <nav aria-label="Marketplace" className="text-sm">
            <h4 className="text-white font-semibold mb-4">Marketplace</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                  Suppliers
                </Link>
              </li>
              <li>
                <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                  Popular Items
                </Link>
              </li>
              <li>
                <Link href="/marketplace" className="text-slate-400 hover:text-blue-400 transition-colors">
                  New Arrivals
                </Link>
              </li>
            </ul>
          </nav>

          {/* Company Links - Desktop */}
          <nav aria-label="Company" className="text-sm">
            <h4 className="text-white font-semibold mb-4">Company</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/about" className="text-slate-400 hover:text-blue-400 transition-colors">
                  About
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-slate-400 hover:text-blue-400 transition-colors">
                  Blogs
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-slate-400 hover:text-blue-400 transition-colors">
                  Careers
                </Link>
              </li>
            </ul>
          </nav>

          {/* Legal & Contact - Desktop */}
          <div className="text-sm">
            <h4 className="text-white font-semibold mb-4">Legal</h4>
            <ul className="space-y-3">
              <li>
                <Link href="/terms" className="text-slate-400 hover:text-blue-400 transition-colors">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-slate-400 hover:text-blue-400 transition-colors">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <a href="mailto:<EMAIL>" className="text-slate-400 hover:text-blue-400 transition-colors">
                  <EMAIL>
                </a>
              </li>
              <li className="text-slate-500">
                Nairobi, Kenya
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="text-center text-sm mt-8 md:mt-12 border-t border-slate-700 pt-6 md:pt-8">
          <p className="text-slate-500">
            &copy; {new Date().getFullYear()} Market O&apos;Clock. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}